---
title: "Frameworks"
description: "Tracing with existing frameworks"
---

import { IntegrationCard } from "../../card";
import { OpenTelemetryLogo, VercelLogo, Langchain<PERSON>ogo, LanggraphLogo } from "../../../../app/app/[org]/onboarding-logos";
import { OpenAI, Anthropic, Meta, Gemini, Mistral, Together, Fireworks, Perplexity, XAI, Groq, Lepton, Cerebras, Ollama, Replicate, Baseten, Amazon, GoogleCloud, Azure, Databricks, Google } from "#/ui/icons/providers";
import { SquareDashedIcon } from "lucide-react";

# Trace with existing frameworks

Trace your apps using existing frameworks to quickly add observability. This guide walks you through the supported frameworks and how to configure them for maximum observability and insight.

<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-5">
  <IntegrationCard href="/docs/start/frameworks/opentelemetry" title="OpenTelemetry">
    <OpenTelemetryLogo size={64} />
  </IntegrationCard>
  <IntegrationCard href="/docs/start/frameworks/vercel-ai-sdk" title="Vercel AI SDK">
    <VercelLogo size={64} />
  </IntegrationCard>
  <IntegrationCard href="/docs/start/frameworks/openai-agents-sdk" title="Agents SDK">
    <OpenAI size={64} />
  </IntegrationCard>
  <IntegrationCard href="/docs/start/frameworks/instructor" title="Instructor">
    <SquareDashedIcon size={64} className="text-primary-400" />
  </IntegrationCard>
  <IntegrationCard href="/docs/start/frameworks/langchain" title="LangChain">
    <LangchainLogo size={64} />
  </IntegrationCard>
  <IntegrationCard href="/docs/start/frameworks/langgraph" title="LangGraph">
    <LanggraphLogo size={64} />
  </IntegrationCard>
  <IntegrationCard href="/docs/start/frameworks/google" title="Google ADK">
    <Google size={64} />
  </IntegrationCard>
</div>
