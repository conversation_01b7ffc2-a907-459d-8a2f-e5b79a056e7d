import { z } from "zod";

const upgrade_modal_opened = z.object({ orgName: z.string() });
const signup = z.object({
  source: z.enum([
    "in_app",
    "cta_button",
    "logged_out_playground",
    "clerk_signup",
  ]),
});
const signin = z.object({
  source: z.enum(["in_app", "cta_button", "logged_out_playground"]),
});

const projectCreateEntryPoints = [
  "projectsSidebarDropdown",
  "projectsHomePageNewProjectButton",
  "projectsHomePageEmptyState",
  "onboardingFlow",
] as const;

export type ProjectCreateEntryPoint = (typeof projectCreateEntryPoints)[number];

const projectCreateAttempt = z.object({
  projectName: z.string(),
  entryPoint: z.enum(projectCreateEntryPoints).optional(),
  flowId: z.string(),
});

const projectCreateAbandon = z.object({
  projectName: z.string(),
  reason: z.enum(["error", "closed"]),
  entryPoint: z.enum(projectCreateEntryPoints).optional(),
  flowId: z.string(),
});

const projectCreate = z.object({
  projectName: z.string(),
  projectId: z.string(),
  entryPoint: z.enum(projectCreateEntryPoints).optional(),
  flowId: z.string(),
});

// Playground analytics schemas
const playgroundCreateEntryPoints = [
  "playgroundsPageEmptyState",
  "playgroundsPageCreateButton",
  "promptDialogCreateButton",
  "functionEditorCreateButton",
  "datasetPageCreateButton",
  "onboardingFlow",
  "projectOverviewCreateButton",
] as const;

export type PlaygroundCreateEntryPoint =
  (typeof playgroundCreateEntryPoints)[number];

const playgroundCreateAttempt = z.object({
  playgroundName: z.string(),
  entryPoint: z.enum(playgroundCreateEntryPoints).optional(),
  flowId: z.string(),
  projectId: z.string().optional(),
  projectName: z.string().optional(),
  initialDataType: z
    .enum(["prompt", "function", "scorer", "dataset", "none"])
    .optional(),
  initialObjectId: z.string().optional(), // ID of the initial object (prompt, function, scorer, dataset)
});

const playgroundCreate = z.object({
  playgroundName: z.string(),
  playgroundId: z.string(),
  entryPoint: z.enum(playgroundCreateEntryPoints).optional(),
  flowId: z.string(),
  projectId: z.string(),
  projectName: z.string(),
  initialDataType: z.enum(["prompt", "function", "scorer", "dataset", "none"]),
  initialObjectId: z.string().optional(), // ID of the initial object (prompt, function, scorer, dataset)
});

// Onboarding analytics events
const signupAttempt = z.object({
  flowId: z.string(),
  source: z.literal("web"),
  entryPoint: z
    .enum(["cta_button", "in_app", "logged_out_playground", "other"])
    .optional(),
});

const onboardingStart = z.object({
  flowId: z.string().optional(),
  pageName: z.string(),
  stepIndex: z.number().optional(), // Dynamic step index (1-based) which is the index of the step in the onboarding flow not the visit number
  userId: z.string().optional(),
  flowName: z
    .enum(["new_user_signup", "existing_user_org_creation", "invitee_signup"])
    .optional(),
  source: z.literal("web"),
});

const onboardingStep = z.object({
  flowId: z.string(),
  flowName: z
    .enum(["new_user_signup", "existing_user_org_creation", "invitee_signup"])
    .optional(),
  stepDetails: z.object({
    stepName: z.string(),
    stepIndex: z.number(), // Dynamic step index (1-based, excluding hidden steps)
    timeSpentSeconds: z.number().optional(),
    skippable: z.boolean(),
    previousPage: z.string().optional(),
    navigationHistory: z.array(z.string()).optional(),
    // Provider-specific data for provider_setup step
    providerType: z.string().optional(),
    errorDetails: z.string().optional(), // For tracking error messages
  }),
  completionType: z.enum(["completed", "skipped", "navigated_away"]).optional(), // How the step ended
  skipReason: z.enum(["user_choice", "timeout", "error"]).optional(),
  pathChoice: z.enum(["sdk", "ui"]).optional(),
  entryPoint: z.enum(["back", "continue", "skip", "direct", "auto"]).optional(), // How the user entered this step
  source: z.literal("web"),
});

const onboardingComplete = z.object({
  flowId: z.string().optional(),
  completionDetails: z.object({
    totalTimeSeconds: z.number(),
    skippedSteps: z.array(z.string()),
    navigationHistory: z.array(z.string()).optional(),
    errorDetails: z.string().optional(), // For tracking error messages
  }),
  flowName: z
    .enum(["new_user_signup", "existing_user_org_creation", "invitee_signup"])
    .optional(),
  pathChoice: z.enum(["sdk", "ui"]).optional(),
  source: z.literal("web"),
});

// Playground edit analytics schemas
const playgroundEditEntryPoints = [
  "runButton",
  "runRow",
  "keyboardShortcut",
  "autoRun",
  "headerPlus",
  "taskPlus",
  "duplication",
  "promptEditor",
  "modelSelector",
  "datasetSelector",
  "scorerSelector",
  "toolSelector",
  "configPanel",
  "other",
] as const;

export type PlaygroundEditEntryPoint = (typeof playgroundEditEntryPoints)[number];

const playgroundEditTypes = [
  "promptChanged",
  "modelChanged",
  "datasetChanged",
  "scorersChanged",
  "toolsChanged",
  "structuredOutputChanged",
  "responseTypeChanged",
  "parserChanged",
  "configChanged", // temperature, maxTokens, etc.
  "nameChanged",
  "descriptionChanged",
  "taskAdded",
  "taskDeleted",
  "messageAdded",
  "messageDeleted",
  "messageRoleChanged",
  "messagePartAdded",
] as const;

export type PlaygroundEditType = (typeof playgroundEditTypes)[number];

const playgroundEdit = z.object({
  playgroundId: z.string(),
  projectId: z.string(),
  // Task context - which task the user is editing
  taskId: z.string().optional(),
  taskIndex: z.number().optional(),
  taskType: z.string().optional(),
  // Trigger entity (human vs AI)
  triggerEntity: z.enum(["human", "ai"]),
  // Human-specific action (when triggerEntity === "human")
  entryPoint: z.enum(playgroundEditEntryPoints).optional(),
  // AI-specific action (when triggerEntity === "ai")
  aiAction: z.enum(["loopEdit", "loopCreate", "loopExecute"]).optional(),
  // AI edit state (when triggerEntity === "ai" and aiAction includes "edit")
  aiEditState: z.enum(["accepted", "rejected"]).optional(),
  // What was edited
  editType: z.enum(playgroundEditTypes),
  // Previous vs new values (not applicable for taskAdded and messageAdded)
  previousValue: z.string().optional(), // What it was before
  newValue: z.string().optional(), // What it is now
  // Edit-specific details and metadata (varies by edit type)
  details: z.record(z.unknown()).optional(),
  // Source tracking
  source: z.literal("web"),
});

// Playground run analytics schemas
const playgroundRunEntryPoints = [
  "runButton",
  "runRow", 
  "keyboardShortcut",
  "autoRun",
  "headerRun",
  "taskRun",
  "experimentButton",
  "other",
] as const;

export type PlaygroundRunEntryPoint = (typeof playgroundRunEntryPoints)[number];

const playgroundRunOutcomes = ["success", "error", "cancelled"] as const;

export type PlaygroundRunOutcome = (typeof playgroundRunOutcomes)[number];

const playgroundRun = z.object({
  playgroundId: z.string(),
  projectId: z.string(),
  runId: z.string(), // Unique identifier for each playground run
  // Run outcome
  outcome: z.enum(playgroundRunOutcomes),
  errorMessage: z.string().optional(),
  // Trigger entity (human vs AI)
  triggerEntity: z.enum(["human", "ai"]),
  // Human-specific action (when triggerEntity === "human")
  entryPoint: z.enum(playgroundRunEntryPoints).optional(),
  // AI-specific action (when triggerEntity === "ai")
  aiAction: z.enum(["loopEdit", "loopCreate", "loopExecute"]).optional(),
  // AI edit state (when triggerEntity === "ai" and aiAction includes "edit")
  aiEditState: z.enum(["accepted", "rejected"]).optional(),
  // Run configuration and metadata
  details: z.record(z.unknown()).optional(),
  // Task-specific details (nested within details)
  taskDetails: z
    .array(
      z.object({
        id: z.string().optional(),
        name: z.string().optional(),
        taskIndex: z.number().optional(),
        type: z.enum(["prompt", "function", "remote_eval"]).optional(),
        model: z.string().optional(),
        modelProvider: z.string().optional(),
        sourceType: z
          .enum(["inline", "saved_prompt", "function", "remote"])
          .optional(),
        toolTypes: z.array(z.string()).optional(),
        executionTime: z.number().optional(),
        parentTaskId: z.string().optional(),
        temperature: z.number().optional(),
        topP: z.number().optional(),
        maxTokens: z.number().optional(),
        streamingEnabled: z.boolean().optional(),
        promptType: z.enum(["chat", "completion"]).optional(),
      }),
    )
    .optional(),
  // Source tracking
  source: z.literal("web"),
});

const playgroundDelete = z.object({
  projectId: z.string(),
  playgroundId: z.string(),
  projectName: z.string().optional(),
  deletionMethod: z
    .enum(["delete_button", "keyboard_shortcut", "batch_delete", "other"])
    .optional(),
  numTasks: z.number().optional(),
  hasDataset: z.boolean().optional(),
  hasScorers: z.boolean().optional(),
  lastModified: z.string().optional(),
  createdTime: z.string().optional(),
  // Source tracking
  source: z.literal("web"),
});

const downgradeProSubscription = z.object({
  reason: z.string(),
  otherReason: z.string().optional(),
});

// Server-side signup success event
const signupSuccess = z.object({
  flowId: z.string().optional(),
  flowName: z
    .enum(["new_user_signup", "invitee_signup"])
    .optional(),
  userId: z.string(),
  signupMethod: z.enum(["email", "google"]).optional(),
  // Common server-side fields
  source: z.literal("server"),
  timestamp: z.string().optional(),
});

// Server-side onboarding start event
const onboardingStartServer = z.object({
  userId: z.string(),
  orgId: z.string().optional(),
  flowName: z
    .enum(["new_user_signup", "existing_user_org_creation", "invitee_signup"])
    .optional(),
  // Common server-side fields
  source: z.literal("server"),
});

// Server-side onboarding complete event
const onboardingCompleteServer = z.object({
  userId: z.string(),
  orgId: z.string().optional(),
  flowName: z
    .enum(["new_user_signup", "existing_user_org_creation", "invitee_signup"])
    .optional(),
  pathChoice: z.enum(["sdk", "ui"]).optional(),
  // Common server-side fields
  source: z.literal("server"),
});

export const eventSchemas = {
  upgrade_modal_opened,
  signup,
  signin,
  projectCreateAttempt,
  projectCreate,
  projectCreateAbandon,
  playgroundCreateAttempt,
  playgroundCreate,
  playgroundEdit,
  downgradeProSubscription,
  signupAttempt,
  onboardingStart,
  onboardingStep,
  onboardingComplete,
  playgroundRun,
  playgroundDelete,
  signupSuccess,
  onboardingStartServer,
  onboardingCompleteServer,
} as const;

type EventSchemas = typeof eventSchemas;
export type EventName = keyof EventSchemas;
export type EventProps<K extends EventName> = z.infer<EventSchemas[K]>;
