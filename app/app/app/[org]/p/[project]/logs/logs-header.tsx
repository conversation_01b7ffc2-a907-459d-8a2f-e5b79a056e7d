import { EntityContextMenu } from "#/ui/entity-context-menu";
import { type GetRowsForExportFn } from "../experiments/[experiment]/(queries)/table-queries";
import { ReviewButton } from "../review-button";
import { toast } from "sonner";
import {
  OptimizationChat,
  useIsLoopEnabled,
} from "#/ui/optimization/optimization-chat";
import { useState } from "react";
import { ConfigureOnlineScoringRuleDialog } from "./configure-online-scoring-rule-dialog";
import { OnlineScorersCombobox } from "#/ui/trace/online-scorers";
import { type ProjectScore } from "@braintrust/typespecs";

export const LogsHeader = ({
  projectId,
  projectName,
  orgName,
  getRowsForExport,
  hideActions,
  columnVisibility,
  onBTQLFilter,
  isReadOnly,
}: {
  projectId: string;
  projectName: string;
  orgName: string;
  getRowsForExport: GetRowsForExportFn;
  hideActions?: boolean;
  columnVisibility?: Record<string, boolean>;
  onBTQLFilter?: (filterText: string) => void;
  isReadOnly?: boolean;
}) => {
  const isLoopEnabled = useIsLoopEnabled();
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [editingRule, setEditingRule] = useState<ProjectScore | null>(null);

  return (
    <div className="flex items-center gap-2 px-3 pb-2 pt-1 bg-primary-50">
      <h1 className="text-base font-semibold">Logs</h1>
      <EntityContextMenu
        objectType="project_log"
        objectId={projectId ?? ""}
        objectName={projectName}
        orgName={orgName}
        projectName={projectName}
        buttonClassName="h-7 w-7"
        getRowsForExport={getRowsForExport}
        exportName={[projectName, "logs"].join(" ")}
        onlyExportingLoadedRows={true}
        columnVisibility={columnVisibility}
        handleCopyId={() => {
          navigator.clipboard.writeText(projectId ?? "");
          toast("Project ID copied to clipboard");
        }}
        isReadOnly={isReadOnly}
      />
      <div className="grow" />
      {!hideActions && (
        <>
          <ReviewButton />
          <OnlineScorersCombobox
            canAddRule
            onCreateRule={() => setIsCreateDialogOpen(true)}
            onEditRule={(rule) => {
              setEditingRule(rule);
              setIsCreateDialogOpen(true);
            }}
          />
          {isLoopEnabled && (
            <OptimizationChat
              hasMultipleSelectedExperiments={false}
              onBTQLFilter={onBTQLFilter}
            />
          )}
        </>
      )}
      <ConfigureOnlineScoringRuleDialog
        open={isCreateDialogOpen}
        onOpenChange={(open) => {
          setIsCreateDialogOpen(open);
          if (!open) {
            setEditingRule(null);
          }
        }}
        selectedRule={editingRule}
        showDelete={true}
      />
    </div>
  );
};
