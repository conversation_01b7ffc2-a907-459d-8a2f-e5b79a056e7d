/*!result -- This should return 3 rows
    length == 3
*/
select: *  | from: experiment('singleton') summary | filter: metrics.duration > 1.5 | sort: _pagination_key desc;

/*!result -- This should return 1 row
    length == 1
*/
select: *  | from: experiment('singleton') summary | filter: metrics.duration > 1.5 | sort: _pagination_key desc | limit: 1;

/*!result -- This should return 1 row
    length == 1
*/
select: *  | from: experiment('singleton') summary | filter: metrics.duration > 1.5 | sort: _pagination_key desc | limit: 1 | cursor: Z7KHbBAvACk;


/*!result -- This should return 2 remaining rows
    length == 2
*/
select: *  | from: experiment('singleton') summary | filter: metrics.duration > 1.5 | sort: _pagination_key desc | cursor: Z7KHbBAvACk;


-- Test transaction id cursors
/*!result -- This should return 3 rows
    length == 3
*/
select: *  | from: experiment('singleton') summary | filter: metrics.duration > 1.5 | sort: _xact_id desc;

/*!result -- This should return 1 row
    length == 1
*/
select: *  | from: experiment('singleton') summary | filter: metrics.duration > 1.5 | sort: _xact_id desc | limit: 1;

/*!result -- This should return 1 row
    length == 1
*/
select: *  | from: experiment('singleton') summary | filter: metrics.duration > 1.5 | sort: _xact_id desc | limit: 1 | cursor: DeFnsodsEC8;


/*!result -- This should return 2 remaining rows
    length == 2
*/
select: *  | from: experiment('singleton') summary | filter: metrics.duration > 1.5 | sort: _xact_id desc | cursor: DeFnsodsEC8;

/*!optimizer -- OR should be pushed down as long as all branches can be
[.. | objects | select(has("TantivySearch")) | .TantivySearch | .. | select(type == "object" and has("BooleanQuery"))] | length > 0
*/
select: * | from: experiment('singleton') summary | filter: scores.foo is not null or scores.bar is not null | sort: _pagination_key desc | limit: 1;

/*!optimizer -- OR should be pushed down as long as all branches can be
[.. | objects | select(has("TantivySearch")) | .TantivySearch | .. | select(type == "object" and has("BooleanQuery"))] | length > 0
*/
select: * | from: experiment('singleton') summary | filter: metrics.duration > 1 and (scores.foo is not null or scores.bar is not null) | sort: _pagination_key desc | limit: 1;

/*!optimizer -- One branch of the OR can't be pushed, so none of it can
[.. | select(. == "AllQuery")] | length == 1
*/
select: * | from: experiment('singleton') summary | filter: scores.foo is not null or metrics.duration > 1 | sort: _pagination_key desc | limit: 1;
