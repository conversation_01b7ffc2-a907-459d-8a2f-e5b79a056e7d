use clap::{Parser, ValueEnum};
use lazy_static::lazy_static;
use otel_common::{
    opentelemetry::{
        metrics::{Counter, Gauge, Histogram},
        KeyValue,
    },
    process_info::get_system_memory,
};
use rand;
use serde::{Deserialize, Serialize};
use std::{collections::HashSet, time::Duration};
use tokio::sync::mpsc;
use tracing::instrument;

use crate::{
    config_with_store::{ConfigWithStore, StoreInfo},
    directory::cached_directory::FileCacheOpts,
    global_store::{
        GlobalStore, RecentObject, RecentObjectCursor, SegmentWalEntriesXactIdStatistic,
    },
    index_document::make_full_schema,
    limits::{default_max_concurrent_index_operations, global_limits},
    merge::{
        default_target_num_segments, merge_tantivy_segments, MergeOpts, MergeTantivySegmentsInput,
        MergeTantivySegmentsOptionalInput, MergeTantivySegmentsOptions,
    },
    process_wal::{
        clear_compacted_index, compact_segment_wal, ClearCompactedIndexInput,
        CompactSegmentWalInput, CompactSegmentWalIrrecoverableError,
        CompactSegmentWalOptionalInput, CompactSegmentWalOptions, ProcessObjectWalOptions,
        XACT_ID_FIELD,
    },
    tantivy_index::{validate_tantivy_index, TantivyIndexScope, TantivyIndexWriterOpts},
    timer::{OtelCounterGuard, OtelTimer},
};
use futures::join;
use util::{
    anyhow::{anyhow, Result},
    config::StorageConfig,
    schema::Schema,
    system_types::{make_object_schema, FullObjectId, FullObjectIdOwned},
    uuid::Uuid,
    xact::{PaginationKey, TransactionId},
};

#[derive(Clone)]
pub struct OptimizeObjectInput<'a> {
    pub segment_ids: &'a [Uuid],
    pub config: ConfigWithStore,
    pub schema: util::schema::Schema,
    pub dry_run: bool,
    pub recompact: bool,
    pub run_async: bool,

    // These come in via input because they get parsed elsewhere.
    pub file_cache_opts: FileCacheOpts,
    pub storage_config: StorageConfig,
}

#[derive(Debug, Clone, Default)]
pub struct OptimizeObjectOptionalInput {
    pub testing_first_compaction_force_irrecoverable_error: Option<bool>,
}

#[derive(Debug, Clone, Parser, Serialize, Deserialize, Default)]
pub struct OptimizeObjectOptions {
    /// Configuration options for writing to the segment index.
    #[command(flatten)]
    #[serde(flatten)]
    pub process_wal_opts: ProcessObjectWalOptions,

    /// Configuration options for writing to the segment index.
    #[command(flatten)]
    #[serde(flatten)]
    pub compact_wal_opts: CompactSegmentWalOptions,

    #[command(flatten)]
    #[serde(flatten)]
    pub merge_opts: MergeOpts,

    #[command(flatten)]
    #[serde(flatten)]
    pub tuning_opts: OptimizeObjectTuningOptions,
}

#[derive(Debug, Clone, Parser, Serialize, Deserialize)]
pub struct OptimizeObjectTuningOptions {
    #[arg(
        long,
        value_parser = util::ByteSize::parse_to_usize,
        default_value_t = default_merge_page_size(),
        env = "BRAINSTORE_MERGE_PAGE_SIZE",
        help = format!(
            "The page size for the merge operation (defaults to {})",
            util::ByteSize::from(default_merge_page_size())
        )
    )]
    #[serde(default = "default_merge_page_size")]
    pub merge_page_size: usize,

    #[arg(
        long,
        value_parser = util::ByteSize::parse_to_usize,
        default_value_t = default_merge_memory_limit(),
        env = "BRAINSTORE_MERGE_MEMORY_LIMIT",
        help = format!(
            "The memory limit for the merge operation (defaults to {})",
            util::ByteSize::from(default_merge_memory_limit())
        )
    )]
    #[serde(default = "default_merge_memory_limit")]
    pub merge_memory_limit: usize,
}

impl Default for OptimizeObjectTuningOptions {
    fn default() -> Self {
        Self {
            merge_page_size: default_merge_page_size(),
            merge_memory_limit: default_merge_memory_limit(),
        }
    }
}

fn default_merge_page_size() -> usize {
    16 * 1024 * 1024 /* 16 MB */
}

fn default_merge_memory_limit() -> usize {
    std::cmp::min(
        8 * 1024 * 1024 * 1024, /* 8 GB */
        get_system_memory() / default_max_concurrent_index_operations(),
    )
}

// One day.
pub const OPTIMIZATION_WORKER_IGNORE_OLD_SEGMENTS_TIME_AGO_SECS: u64 = 24 * 60 * 60;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OptimizeObjectOutput {
    pub performed: Vec<PerformedOptimizationStep>,
    pub planned: Vec<OptimizationStep>,
}

#[instrument(err, skip(input, options), fields(segment_ids = ?input.segment_ids))]
pub async fn optimize_segments<'a>(
    input: OptimizeObjectInput<'a>,
    optional_input: OptimizeObjectOptionalInput,
    options: OptimizeObjectOptions,
) -> Result<OptimizeObjectOutput> {
    // Procedure:
    //
    // 1. Acquire segment information for the object (optionally scoped to the given segment_ids), and
    //    construct a list of when each segment was last processed, the compaction delta, and the
    //    number of rows in the segment.
    // 2. Compute the steps that would be performed.
    // 3. If dry_run, return the steps that would be performed.
    // 4. Otherwise, perform the steps. We try to perform only as many steps as there are indexing operations
    //    available. As soon as we hit enough rows, we stop.
    //
    // Returns the list of completed index operations and the list of steps that would be performed.

    let steps = construct_optimizable_segments(
        input.segment_ids,
        &input.config,
        OptimizationStepOptions {
            target_num_segments: options.merge_opts.target_num_segments as u64,
            recompact: input.recompact,
            testing_first_compaction_force_irrecoverable_error: optional_input
                .testing_first_compaction_force_irrecoverable_error
                .unwrap_or(false),
            ..Default::default()
        },
    )
    .await?;

    if input.dry_run {
        return Ok(OptimizeObjectOutput {
            performed: vec![],
            planned: steps,
        });
    }

    let planned = steps.clone();
    let output = async move {
        let (mut worker, mut completed_receiver) =
            OptimizationWorker::new(OptimizationWorkerInput {
                config: input.config.clone(),
                file_cache_opts: input.file_cache_opts,
                tuning_opts: options.tuning_opts.clone(),
                merge_opts: options.merge_opts.clone(),
                compact_opts: options.compact_wal_opts.clone(),
                process_wal_opts: options.process_wal_opts.clone(),
                budget: 1.0,
            });

        let mut copied_steps = steps.clone();

        let collect_completed_steps = tokio::spawn(async move {
            let mut performed_steps = Vec::new();
            while let Some(step) = completed_receiver.recv().await {
                performed_steps.push(step);
            }
            performed_steps
        });

        for step in steps.into_iter() {
            worker
                .submit(RunnableOptimizationStep {
                    step,
                    schema: input.schema.clone(),
                })
                .await?;
        }

        worker.shutdown().await?;
        let performed_steps = collect_completed_steps.await?;

        copied_steps.retain(|step| !performed_steps.iter().any(|s| s.step == *step));

        Ok(OptimizeObjectOutput {
            performed: performed_steps,
            planned: copied_steps,
        })
    };

    if input.run_async {
        tokio::spawn(async move {
            let result = output.await;
            if let Err(e) = result {
                log::error!("Error optimizing object: {:?}", e);
            }
        });
        Ok(OptimizeObjectOutput {
            performed: vec![],
            planned,
        })
    } else {
        output.await
    }
}

pub struct OptimizableSegment {
    pub segment_id: Uuid,
    pub last_processed_xact_id: Option<TransactionId>,
    pub last_compacted_xact_id: Option<TransactionId>,
    pub num_tantivy_segments: u64,
    pub num_rows: u64,
    pub min_pagination_key: PaginationKey,
    pub has_stats: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct OptimizationStep {
    pub segment_id: Uuid,
    pub recompact: bool,
    pub compact: bool,
    pub merge: bool,

    pub testing_first_compaction_force_irrecoverable_error: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformedOptimizationStep {
    #[serde(flatten)]
    pub step: OptimizationStep,
    pub num_rows_processed: u64,
    pub error: Option<String>,
    pub elapsed_time_ms: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OptimizationStepOptions {
    pub target_num_segments: u64,
    pub ignore_old_segments: bool,
    pub compact: bool,
    pub merge: bool,
    pub recompact: bool,
    pub testing_first_compaction_force_irrecoverable_error: bool,
}

impl Default for OptimizationStepOptions {
    fn default() -> Self {
        Self {
            target_num_segments: default_target_num_segments() as u64,
            ignore_old_segments: false,
            compact: true,
            merge: true,
            recompact: false,
            testing_first_compaction_force_irrecoverable_error: false,
        }
    }
}

async fn construct_optimizable_object(
    object_id: FullObjectId<'_>,
    config: &ConfigWithStore,
    options: OptimizationStepOptions,
) -> Result<Vec<OptimizationStep>> {
    let segment_ids = config
        .global_store
        .list_segment_ids(&[object_id], None)
        .await?
        .remove(0);

    let optimizable_segments =
        construct_optimizable_segments(&segment_ids, config, options).await?;

    Ok(optimizable_segments)
}

async fn construct_optimizable_segments(
    segment_ids: &[Uuid],
    config: &ConfigWithStore,
    options: OptimizationStepOptions,
) -> Result<Vec<OptimizationStep>> {
    let (segment_metas, segment_processed_xact_ids, latest_stats) = join!(
        config.global_store.query_segment_metadatas(segment_ids),
        config
            .global_store
            .query_segment_wal_entries_xact_id_statistic(
                segment_ids,
                SegmentWalEntriesXactIdStatistic::Max,
                None
            ),
        config
            .global_store
            .query_field_statistics(segment_ids, &[XACT_ID_FIELD]),
    );

    let segment_metas = segment_metas?;
    let segment_processed_xact_ids = segment_processed_xact_ids?;
    let latest_stats = latest_stats?;

    let optimizable_segments = segment_metas
        .iter()
        .enumerate()
        .map(|(i, meta)| OptimizableSegment {
            segment_id: segment_ids[i],
            last_processed_xact_id: segment_processed_xact_ids[i],
            last_compacted_xact_id: meta
                .last_compacted_index_meta
                .as_ref()
                .map(|meta| meta.xact_id),
            num_tantivy_segments: meta
                .last_compacted_index_meta
                .as_ref()
                .map(|meta| meta.tantivy_meta.segments.len() as u64)
                .unwrap_or(0),
            num_rows: meta.num_rows,
            min_pagination_key: meta.minimum_pagination_key,
            has_stats: latest_stats
                .get(&segment_ids[i])
                .and_then(|x| x.get(XACT_ID_FIELD))
                .is_some(),
        })
        .filter(|segment| {
            if options.ignore_old_segments {
                // Include only segments that were written to within the last 24 hours. If there is
                // no last_processed_xact_id, we shouldn't need to explicitly skip here because
                // there is no work to do.
                if let Some(last_processed_xact_id) = segment.last_processed_xact_id {
                    let cutoff = TransactionId::from_time_ago(
                        Duration::from_secs(OPTIMIZATION_WORKER_IGNORE_OLD_SEGMENTS_TIME_AGO_SECS),
                        0,
                    );
                    let should_skip = last_processed_xact_id < cutoff;
                    if should_skip {
                        log::info!(
                            "[optimization_worker] Ignoring old segment {}",
                            segment.segment_id,
                        );
                    } else {
                        log::info!("[optimization_worker] Not ignoring recently-written segment {}", segment.segment_id);
                    }
                    return !should_skip;
                } else {
                    log::info!("[optimization_worker] Not ignoring segment {}, because it has no last_processed_xact_id", segment.segment_id);
                }
            }
            true
        })
        .collect();

    Ok(construct_optimization_steps(optimizable_segments, options))
}

pub fn construct_optimization_steps(
    mut optimizable_segments: Vec<OptimizableSegment>,
    options: OptimizationStepOptions,
) -> Vec<OptimizationStep> {
    let mut steps = Vec::new();

    // Prioritize the segments with the most segments to merge.
    optimizable_segments.sort_by_key(|s| -(s.num_tantivy_segments as i64));

    // Compact the newest segments first (in descending order, for each segment whose last
    // processed xact_id is greater than the last compacted xact_id).
    for segment in optimizable_segments.iter() {
        if options.recompact {
            steps.push(OptimizationStep {
                segment_id: segment.segment_id,
                recompact: true,
                compact: true,
                merge: true,
                testing_first_compaction_force_irrecoverable_error: options
                    .testing_first_compaction_force_irrecoverable_error,
            });
            continue;
        }

        let compact = options.compact
            && ((segment.last_processed_xact_id > segment.last_compacted_xact_id)
                || !segment.has_stats);

        let merge = options.merge && (segment.num_tantivy_segments > options.target_num_segments);

        if compact || merge {
            steps.push(OptimizationStep {
                segment_id: segment.segment_id,
                recompact: false,
                compact,
                merge,
                testing_first_compaction_force_irrecoverable_error: options
                    .testing_first_compaction_force_irrecoverable_error,
            });
        }
    }

    steps
}

#[derive(Clone)]
pub struct OptimizeAllObjectsInput {
    pub config: ConfigWithStore,
    pub schema: Option<util::schema::Schema>,
    pub loop_config: OptimizationLoopConfig,
    pub max_iterations: Option<u64>,
    pub storage_config: StorageConfig,
    pub file_cache_opts: FileCacheOpts,
    pub ignore_old_segment_object_ids: HashSet<FullObjectIdOwned>,
}

#[derive(Debug, Clone, Parser, Serialize, Deserialize)]
pub struct OptimizeAllObjectsOptions {
    #[command(flatten)]
    #[serde(flatten)]
    pub optimize_opts: OptimizeObjectOptions,

    /// The maximum number of objects to process in a single optimization iteration.
    #[arg(
        long,
        default_value_t = 100,
        env = "BRAINSTORE_OPTIMIZE_ALL_OBJECTS_BATCH_SIZE"
    )]
    #[serde(default = "default_batch_size")]
    pub batch_size: u64,

    #[arg(long, env = "BRAINSTORE_OPTIMIZE_ALL_OBJECTS_NO_JITTER")]
    #[serde(default)]
    pub no_jitter: bool,
}

fn default_batch_size() -> u64 {
    100
}

impl Default for OptimizeAllObjectsOptions {
    fn default() -> Self {
        Self {
            optimize_opts: Default::default(),
            batch_size: default_batch_size(),
            no_jitter: false,
        }
    }
}

pub struct OptimizationLoopOutput {
    // This number isn't a true "count distinct", because if an object is encountered twice,
    // it's counted twice. But it helps to get a sense of how many objects were processed.
    pub num_objects: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OptimizationLoopConfig {
    pub name: &'static str,
    pub compact: bool,
    pub merge: bool,
    pub min_compaction_lag_seconds: Option<u64>,
    pub min_processed_seconds_ago: u64,
    pub max_processed_seconds_ago: u64,
    // The fraction of indexing budget to dedicate to this optimization loop.
    pub budget: f64,
}

pub const LONG_TERM_OPTIMIZATION_LOOP_CONFIG: OptimizationLoopConfig = OptimizationLoopConfig {
    name: "long_term",
    compact: true,
    merge: true,
    min_compaction_lag_seconds: None,
    min_processed_seconds_ago: 0,
    max_processed_seconds_ago: 30 * 24 * 60 * 60,
    budget: 0.5,
};
pub const SHORT_TERM_OPTIMIZATION_LOOP_CONFIG: OptimizationLoopConfig = OptimizationLoopConfig {
    name: "short_term",
    compact: true,
    merge: false,
    min_compaction_lag_seconds: Some(60), // 1 minute
    min_processed_seconds_ago: 0,
    max_processed_seconds_ago: 2 * 24 * 3600, // 2 days
    budget: 0.5,
};

#[derive(Debug, Clone, Serialize, Deserialize, ValueEnum)]
#[serde(rename_all = "lowercase")]
pub enum OptimizationLoopType {
    LongTerm,
    ShortCompact,
}

impl OptimizationLoopType {
    pub fn to_config(&self) -> OptimizationLoopConfig {
        match self {
            OptimizationLoopType::LongTerm => LONG_TERM_OPTIMIZATION_LOOP_CONFIG,
            OptimizationLoopType::ShortCompact => SHORT_TERM_OPTIMIZATION_LOOP_CONFIG,
        }
        .clone()
    }
}

struct Meters {
    pub frontier_seconds_ago: Gauge<u64>,
    pub position_seconds_ago: Gauge<u64>,
    pub objects_considered: Counter<u64>,
    pub objects_considered_within_frontier: Counter<u64>,
    pub objects_optimized: Counter<u64>,
    pub segments_optimized: Counter<u64>,
    pub submit_time: Histogram<u64>,
    pub compact: OtelTimer,
    pub merge: OtelTimer,
}

impl Default for Meters {
    fn default() -> Self {
        let meter = otel_common::opentelemetry::global::meter("brainstore");
        Self {
            frontier_seconds_ago: meter
                .u64_gauge("brainstore.storage.optimization_loop.frontier_seconds_ago")
                .build(),
            position_seconds_ago: meter
                .u64_gauge("brainstore.storage.optimization_loop.position_seconds_ago")
                .build(),
            objects_considered: meter
                .u64_counter("brainstore.storage.optimization_loop.objects_considered")
                .build(),
            objects_considered_within_frontier: meter
                .u64_counter(
                    "brainstore.storage.optimization_loop.objects_considered_within_frontier",
                )
                .build(),
            objects_optimized: meter
                .u64_counter("brainstore.storage.optimization_loop.objects_optimized")
                .build(),
            segments_optimized: meter
                .u64_counter("brainstore.storage.optimization_loop.segments_optimized")
                .build(),
            submit_time: meter
                .u64_histogram("brainstore.storage.optimization_loop.submit_time_ms")
                .with_unit("ms")
                .with_description("Time taken to submit an optimization step")
                .build(),
            compact: OtelTimer::new(&meter, "brainstore.storage.optimization_loop.compact"),
            merge: OtelTimer::new(&meter, "brainstore.storage.optimization_loop.merge"),
        }
    }
}
lazy_static! {
    static ref METERS: Meters = Meters::default();
}

pub async fn run_optimization_loop(
    input: OptimizeAllObjectsInput,
    options: OptimizeAllObjectsOptions,
) -> Result<OptimizationLoopOutput> {
    // This function implements an optimization loop that tries to optimize storage for all objects _up until_ the loop_state.frontier
    // (i.e. whose last_processed_xact_id >= input.frontier). Once it finishes a round of iteration, it resets, and goes back to the same frontier.
    // There are currently two loops with different frontiers. The short term loop goes back to the last 2 days, and the long term loop to the last 30.
    //
    // As it runs, it submits optimization jobs to the worker which runs in a separate task. Once a job is finished, we receive a message
    // on the receiver channel, which tells us how much work was done. If the step processed any rows, then we re-submit the object to the worker
    // to keep chewing on it until it's a no-op.

    let (mut worker, mut completed_receiver) = OptimizationWorker::new(OptimizationWorkerInput {
        config: input.config.clone(),
        file_cache_opts: input.file_cache_opts,
        tuning_opts: options.optimize_opts.tuning_opts.clone(),
        merge_opts: options.optimize_opts.merge_opts.clone(),
        compact_opts: options.optimize_opts.compact_wal_opts.clone(),
        process_wal_opts: options.optimize_opts.process_wal_opts.clone(),
        budget: input.loop_config.budget,
    });

    // This worker drains finished steps from the background receiver and just logs them. We don't currently do
    // anything with them.
    let background_receiver = tokio::spawn(async move {
        while let Some(step) = completed_receiver.recv().await {
            if step.num_rows_processed > 0 {
                log::info!(
                    "[run_optimization_loop] [{}] finished running step: {:?}",
                    input.loop_config.name,
                    step,
                );
            }
        }

        log::info!(
            "[run_optimization_loop] [{}] background receiver closed",
            input.loop_config.name
        );
        Ok::<_, util::anyhow::Error>(())
    });

    let mut iterations = 0;
    let mut result = OptimizationLoopOutput { num_objects: 0 };
    loop {
        // This inner loop works from the most recently written object "up to" the frontier, using the cursor to track
        // its progress along the way to the frontier.
        let mut cursor: Option<RecentObject> = None;

        // For the first iteration, pick a time uniformly between 0 and the
        // max_processed_seconds_ago, so that we get some jitter across machines
        let jittered_seconds = if iterations == 0 && !options.no_jitter {
            rand::random::<u64>() % input.loop_config.max_processed_seconds_ago
        } else {
            input.loop_config.max_processed_seconds_ago
        };

        let frontier = TransactionId::from_time_ago(Duration::from_secs(jittered_seconds), 0);
        let mut object_iterations = 0;

        METERS.frontier_seconds_ago.record(
            input.loop_config.min_processed_seconds_ago,
            &[KeyValue::new("loop", input.loop_config.name)],
        );

        loop {
            let mut object_batch = input
                .config
                .global_store
                .query_recently_updated_objects(
                    cursor.as_ref().map(|c| c.cursor()).or_else(|| {
                        if input.loop_config.min_processed_seconds_ago > 0 {
                            Some(RecentObjectCursor {
                                last_processed_xact_id: TransactionId::from_time_ago(
                                    Duration::from_secs(
                                        input.loop_config.min_processed_seconds_ago,
                                    ),
                                    0,
                                ),
                                object_id: None,
                            })
                        } else {
                            None
                        }
                    }),
                    options.batch_size as usize,
                    input.loop_config.min_compaction_lag_seconds,
                )
                .await?;

            // Trim away any objects that are past the frontier we're supposed to optimize up to.
            object_batch.retain(|o| o.last_processed_xact_id >= frontier);

            for object in object_batch.iter() {
                METERS.objects_considered.add(
                    1u64,
                    &[
                        KeyValue::new("object_type", object.object_id.object_type.to_string()),
                        KeyValue::new("loop", input.loop_config.name),
                    ],
                );
            }

            if object_batch.is_empty() {
                // In the short term loop, and when optimization is generally healthy, this is
                // highly likely to happen, which is fine. It just means we'll reset the cursor
                // and see the "current position" of the optimization loop stay close to real-time.
                break;
            }

            for object in object_batch {
                let time_ago = object.last_processed_xact_id.duration_since_now();
                METERS.position_seconds_ago.record(
                    time_ago.as_secs(),
                    &[KeyValue::new("loop", input.loop_config.name)],
                );

                METERS.objects_considered_within_frontier.add(
                    1,
                    &[
                        KeyValue::new("object_type", object.object_id.object_type.to_string()),
                        KeyValue::new("loop", input.loop_config.name),
                    ],
                );

                let steps = construct_optimizable_object(
                    object.object_id.as_ref(),
                    &input.config,
                    OptimizationStepOptions {
                        target_num_segments: options.optimize_opts.merge_opts.target_num_segments
                            as u64,
                        compact: input.loop_config.compact,
                        merge: input.loop_config.merge,
                        recompact: false,
                        ignore_old_segments: {
                            let should_ignore = input
                                .ignore_old_segment_object_ids
                                .contains(&object.object_id);
                            if should_ignore {
                                log::info!(
                                    "[run_optimization_loop] [{}] ignoring old segments from object {}",
                                    input.loop_config.name,
                                    object.object_id
                                );
                            }
                            should_ignore
                        },
                        testing_first_compaction_force_irrecoverable_error: false,
                    },
                )
                .await?;

                let schema = match &input.schema {
                    Some(schema) => make_full_schema(schema)?,
                    None => make_full_schema(&make_object_schema(object.object_id.object_type)?)?,
                };

                if !steps.is_empty() {
                    object_iterations += 1;

                    log::info!(
                        "[run_optimization_loop] [{}] optimizing object: {}, steps: {:?}",
                        input.loop_config.name,
                        object.object_id,
                        steps
                    );
                    METERS.objects_optimized.add(
                        1,
                        &[
                            KeyValue::new("object_type", object.object_id.object_type.to_string()),
                            KeyValue::new("loop", input.loop_config.name),
                        ],
                    );
                }

                for step in steps {
                    let step_compact = step.compact;
                    let step_merge = step.merge;
                    let segment_id = step.segment_id;
                    METERS.segments_optimized.add(
                        1,
                        &[
                            KeyValue::new("object_type", object.object_id.object_type.to_string()),
                            KeyValue::new("compact", step_compact),
                            KeyValue::new("merge", step_merge),
                            KeyValue::new("loop", input.loop_config.name),
                        ],
                    );

                    let start = std::time::Instant::now();
                    worker
                        .submit(RunnableOptimizationStep {
                            step,
                            schema: schema.clone(),
                        })
                        .await?;

                    let submit_time = start.elapsed();
                    METERS.submit_time.record(
                        submit_time.as_millis() as u64,
                        &[
                            KeyValue::new("object_type", object.object_id.object_type.to_string()),
                            KeyValue::new("compact", step_compact),
                            KeyValue::new("merge", step_merge),
                            KeyValue::new("loop", input.loop_config.name),
                        ],
                    );

                    log::info!(
                        "[run_optimization_loop] [{}] submitted optimization for object: {}, segment: {} (compact: {}, merge: {}) in {:?}",
                        input.loop_config.name,
                        object.object_id,
                        segment_id,
                        step_compact,
                        step_merge,
                        submit_time,
                    );
                }

                // Whereas the cursor helps us proceed within the current call to loop_iteration.
                cursor = match cursor {
                    Some(c) => Some(c.min(object)),
                    _ => Some(object),
                };
            }
        }

        result.num_objects += object_iterations;

        iterations += 1;
        if input
            .max_iterations
            .map(|max_iterations| iterations >= max_iterations)
            .unwrap_or(false)
        {
            log::info!(
                "[run_optimization_loop] [{}] Reached max iterations ({}), stopping",
                input.loop_config.name,
                iterations
            );
            break;
        }

        if object_iterations == 0 {
            log::debug!(
                "[run_optimization_loop] [{}] No objects to optimize, sleeping for 10 seconds (frontier: {}, cursor: {:?})",
                input.loop_config.name,
                frontier,
                cursor
            );
            tokio::time::sleep(tokio::time::Duration::from_secs(10)).await;
        }
    }

    // Wait for the background receiver to shut down.
    worker.shutdown().await?;

    // Wait for the background receiver task to shut down.
    background_receiver.await??;

    Ok(result)
}

struct OptimizationWorker {
    sender: Option<mpsc::Sender<RunnableOptimizationStep>>,
    consumer: tokio::task::JoinHandle<Result<()>>,
}

struct OptimizationWorkerInput {
    config: ConfigWithStore,
    file_cache_opts: FileCacheOpts,
    tuning_opts: OptimizeObjectTuningOptions,
    merge_opts: MergeOpts,
    compact_opts: CompactSegmentWalOptions,
    process_wal_opts: ProcessObjectWalOptions,
    budget: f64,
}

impl OptimizationWorker {
    pub fn new(args: OptimizationWorkerInput) -> (Self, mpsc::Receiver<PerformedOptimizationStep>) {
        let OptimizationWorkerInput {
            config,
            file_cache_opts,
            tuning_opts,
            merge_opts,
            compact_opts,
            process_wal_opts,
            budget,
        } = args;
        let max_concurrent_ops =
            (budget * (global_limits().index_operations.capacity() as f64)) as usize;

        // The limit on these channels is not strictly necessary but aligns with the concurrency limit enforced within the consumer.
        let (sender, receiver) = mpsc::channel(max_concurrent_ops);
        let (completed_sender, completed_receiver) = mpsc::channel(max_concurrent_ops);

        let consumer = OptimizationWorkerConsumer {
            receiver,
            opts: OptimizationWorkerOpts {
                completed: completed_sender,
                config,
                file_cache_opts,
                merge_page_size: tuning_opts.merge_page_size,
                merge_memory_limit: tuning_opts.merge_memory_limit,
                merge_opts,
                compact_opts,
                process_wal_opts,
            },
        };

        (
            Self {
                sender: Some(sender),
                consumer: consumer.start(),
            },
            completed_receiver,
        )
    }

    pub async fn submit(&mut self, step: RunnableOptimizationStep) -> Result<()> {
        self.sender.as_ref().unwrap().send(step).await?;
        Ok(())
    }

    pub async fn shutdown(mut self) -> Result<()> {
        drop(self.sender.take().unwrap());
        self.consumer.await??;
        Ok(())
    }
}

struct OptimizationWorkerConsumer {
    receiver: mpsc::Receiver<RunnableOptimizationStep>,
    opts: OptimizationWorkerOpts,
}

#[derive(Clone)]
struct OptimizationWorkerOpts {
    completed: mpsc::Sender<PerformedOptimizationStep>,

    config: ConfigWithStore,
    file_cache_opts: FileCacheOpts,
    merge_page_size: usize,
    merge_memory_limit: usize,

    merge_opts: MergeOpts,
    compact_opts: CompactSegmentWalOptions,
    process_wal_opts: ProcessObjectWalOptions,
}

impl OptimizationWorkerOpts {
    pub fn new_directory(&self, is_merge: bool) -> Result<ConfigWithStore> {
        let cache_opts = if is_merge {
            FileCacheOpts {
                page_size: self.merge_page_size,
                memory_limit: self.merge_memory_limit,
                ..self.file_cache_opts.clone()
            }
        } else {
            self.file_cache_opts.clone()
        };

        self.config.with_new_indexing_directory(cache_opts)
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
struct RunnableOptimizationStep {
    step: OptimizationStep,
    schema: Schema,
}

const MAX_OPTIMIZATION_ITERATIONS: usize = 100;

impl OptimizationWorkerConsumer {
    fn start(mut self) -> tokio::task::JoinHandle<Result<()>> {
        tokio::spawn(async move {
            let mut set = tokio::task::JoinSet::new();

            loop {
                while set.len() >= self.receiver.max_capacity() {
                    if let Some(res) = set.join_next().await {
                        // This only fails when the task fails to join.
                        res.unwrap();
                    }
                }

                match self.receiver.recv().await {
                    Some(step) => {
                        set.spawn(OptimizationWorkerConsumer::run_step(
                            self.opts.clone(),
                            step,
                        ));
                    }
                    None => break,
                }
            }

            set.join_all().await;

            Ok(())
        })
    }

    async fn run_step(opts: OptimizationWorkerOpts, cmd: RunnableOptimizationStep) {
        let step = cmd.step.clone();
        let start_time = std::time::Instant::now();
        let processed_rows = Self::run_step_inner(&opts, cmd).await;
        let elapsed_time_ms = start_time.elapsed().as_millis() as u64;

        let step = match processed_rows {
            Ok(iter_processed_rows) => PerformedOptimizationStep {
                step,
                num_rows_processed: iter_processed_rows as u64,
                error: None,
                elapsed_time_ms,
            },
            Err(e) => PerformedOptimizationStep {
                step,
                num_rows_processed: 0,
                error: Some(e.to_string()),
                elapsed_time_ms,
            },
        };

        opts.completed.send(step).await.unwrap();
    }

    async fn run_step_inner(
        opts: &OptimizationWorkerOpts,
        cmd: RunnableOptimizationStep,
    ) -> Result<usize> {
        let mut iters = 0;
        let mut total_rows = 0;
        let mut should_recompact = cmd.step.recompact;
        let mut testing_compaction_force_irrecoverable_error =
            cmd.step.testing_first_compaction_force_irrecoverable_error;
        while iters < MAX_OPTIMIZATION_ITERATIONS {
            let mut iter_processed_rows = 0;

            if should_recompact {
                match clear_compacted_index(ClearCompactedIndexInput {
                    segment_id: cmd.step.segment_id,
                    global_store: opts.config.global_store.clone(),
                    locks_manager: &*opts.config.locks_manager,
                })
                .await
                {
                    Ok(()) => {
                        should_recompact = false;
                    }
                    Err(e) => {
                        log::error!("Error clearing compacted index: {:?}", e);
                        return Err(e);
                    }
                };
            }

            if cmd.step.compact {
                let _timer_guard = OtelCounterGuard::new(&METERS.compact);
                let config = opts.new_directory(false)?;

                match compact_segment_wal(
                    CompactSegmentWalInput {
                        segment_id: cmd.step.segment_id,
                        index_store: config.index.clone(),
                        schema: cmd.schema.clone(),
                        global_store: config.global_store.clone(),
                        locks_manager: &*config.locks_manager,
                    },
                    CompactSegmentWalOptionalInput {
                        try_acquire: true,
                        use_status_updater: true,
                        testing_force_irrecoverable_error: std::mem::take(
                            &mut testing_compaction_force_irrecoverable_error,
                        ),
                        ..Default::default()
                    },
                    opts.compact_opts.clone(),
                )
                .await
                {
                    Ok(r) => {
                        iter_processed_rows += r.num_wal_entries_compacted;
                    }
                    Err(e) => {
                        log::error!("Error compacting segment {}: {:?}", cmd.step.segment_id, e);
                        if e.downcast_ref::<CompactSegmentWalIrrecoverableError>()
                            .is_some()
                        {
                            log::warn!(
                                "Detected unrecoverable error for segment {}. {:?}. Will recompact.",
                                cmd.step.segment_id,
                                e
                            );
                            should_recompact = true;
                            continue;
                        } else if should_recompact_invalid_segment(
                            cmd.step.segment_id,
                            &config.index,
                            opts.config.global_store.as_ref(),
                            &opts.compact_opts.writer_opts,
                        )
                        .await?
                        {
                            should_recompact = true;
                            continue;
                        }
                        return Err(e);
                    }
                };
            }

            if cmd.step.merge {
                let _timer_guard = OtelCounterGuard::new(&METERS.merge);
                let config = opts.new_directory(true)?;
                match merge_tantivy_segments(
                    MergeTantivySegmentsInput {
                        segment_id: cmd.step.segment_id,
                        config: config.clone(),
                        schema: &cmd.schema,
                        dry_run: false,
                        try_acquire: true,
                    },
                    MergeTantivySegmentsOptionalInput {
                        use_status_updater: true,
                        ..Default::default()
                    },
                    MergeTantivySegmentsOptions {
                        merge_opts: opts.merge_opts.clone(),
                        writer_opts: opts.compact_opts.writer_opts.clone(),
                        process_wal_opts: opts.process_wal_opts.clone(),
                    },
                )
                .await
                {
                    Ok(r) => {
                        iter_processed_rows += r.num_rows;
                    }
                    Err(e) => {
                        log::error!("Error merging segment: {:?}", e);
                        if should_recompact_invalid_segment(
                            cmd.step.segment_id,
                            &config.index,
                            opts.config.global_store.as_ref(),
                            &opts.compact_opts.writer_opts,
                        )
                        .await?
                        {
                            should_recompact = true;
                            continue;
                        }
                        return Err(e);
                    }
                };
            }

            if iter_processed_rows == 0 {
                // If we didn't process any rows, then we're done.
                break;
            }

            total_rows += iter_processed_rows;
            iters += 1;
        }

        if iters == MAX_OPTIMIZATION_ITERATIONS {
            log::warn!(
                "Reached max iterations ({}) for segment {} after processing {} rows, stopping",
                iters,
                cmd.step.segment_id,
                total_rows
            );
        }

        Ok(total_rows)
    }
}

async fn should_recompact_invalid_segment(
    segment_id: Uuid,
    index: &StoreInfo,
    global_store: &dyn GlobalStore,
    writer_opts: &TantivyIndexWriterOpts,
) -> Result<bool> {
    let segment_metas = global_store.query_segment_metadatas(&[segment_id]).await?;
    let segment_meta = segment_metas.first().unwrap();
    let index_meta = match &segment_meta.last_compacted_index_meta.as_ref() {
        Some(meta) => &meta.tantivy_meta,
        None => {
            return Err(anyhow!(
                "No compacted index metadata found for segment {}",
                segment_id
            ));
        }
    };
    let validation_result = validate_tantivy_index(
        index_meta.clone(),
        &index.store,
        &index.prefix,
        &TantivyIndexScope::Segment(segment_id),
        &writer_opts.validate_opts,
    )
    .await?;
    match validation_result.check_success() {
        Ok(validated_meta) => {
            log::info!(
                "Index validation succeeded for segment {}. last_compacted_index_meta: {:?}",
                segment_id,
                serde_json::to_string(&validated_meta)?
            );
            Ok(false)
        }
        Err(e) => {
            log::warn!(
                "Index validation failed for segment {}. {}. Will recompact.",
                segment_id,
                e
            );
            Ok(true)
        }
    }
}
