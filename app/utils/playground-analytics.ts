"use server";

import { trackServerEvent } from "#/utils/server-analytics";
import type { AuthLookup } from "#/utils/server-util";

export type PlaygroundRunEvent = {
  playgroundId: string;
  projectId: string | null;
  runId: string; // Unique identifier for each playground run
  outcome: "success" | "error" | "cancelled";
  errorMessage?: string;
  details?: Record<string, unknown>;
  taskDetails?: Array<{
    id?: string;
    name?: string;
    taskIndex?: number;
    type?: "prompt" | "function" | "remote_eval";
    model?: string;
    modelProvider?: string;
    sourceType?: "inline" | "saved_prompt" | "function" | "remote";
    toolTypes?: string[];
    executionTime?: number;
    parentTaskId?: string;
    temperature?: number;
    topP?: number;
    maxTokens?: number;
    streamingEnabled?: boolean;
    promptType?: "chat" | "completion";
  }>;
};

/**
 * Track a successful playground creation
 */
export async function trackPlaygroundCreate(
  event: {
    playgroundName: string;
    playgroundId: string;
    projectId: string;
    projectName: string;
    initialDataType: "prompt" | "function" | "scorer" | "dataset" | "none";
    initialObjectId?: string; // ID of the initial object (prompt, function, scorer, dataset)
  },
  authLookupRaw?: AuthLookup,
): Promise<void> {
  await trackServerEvent("playgroundCreate", event, {
    authLookup: authLookupRaw,
  });
}

/**
 * Track a playground run event
 * This function can be called from server actions or API routes
 */
export async function trackPlaygroundRun(
  event: PlaygroundRunEvent,
  authLookupRaw?: AuthLookup,
): Promise<{ success: true }> {
  await trackServerEvent("playgroundRun", event, {
    authLookup: authLookupRaw,
  });
  return { success: true };
}

/**
 * Track a successful playground run
 */
export async function trackPlaygroundRunSuccess(
  event: Omit<PlaygroundRunEvent, "outcome">,
  authLookupRaw?: AuthLookup,
): Promise<void> {
  await trackPlaygroundRun({ ...event, outcome: "success" }, authLookupRaw);
}

/**
 * Track a failed playground run
 */
export async function trackPlaygroundRunError(
  event: Omit<PlaygroundRunEvent, "outcome"> & { errorMessage: string },
  authLookupRaw?: AuthLookup,
): Promise<void> {
  await trackPlaygroundRun({ ...event, outcome: "error" }, authLookupRaw);
}

/**
 * Track a cancelled playground run
 */
export async function trackPlaygroundRunCancelled(
  event: Omit<PlaygroundRunEvent, "outcome">,
  authLookupRaw?: AuthLookup,
): Promise<void> {
  await trackPlaygroundRun({ ...event, outcome: "cancelled" }, authLookupRaw);
}

/**
 * Track a playground delete event
 * This function can be called from server actions or API routes
 */
export async function trackPlaygroundDelete(
  event: {
    playgroundId: string;
    projectId: string;
    projectName?: string;
    deletionMethod?: "delete_button" | "keyboard_shortcut" | "batch_delete" | "other";
    numTasks?: number;
    hasDataset?: boolean;
    hasScorers?: boolean;
    lastModified?: string;
    createdTime?: string;
  },
  authLookupRaw?: AuthLookup,
): Promise<{ success: true }> {
  await trackServerEvent("playgroundDelete", event, {
    authLookup: authLookupRaw,
  });
  return { success: true };
}
