import { type SetValue } from "#/lib/clientDataStorage";
import {
  codeFolding,
  ensureSyntaxTree,
  foldEffect,
  foldNodeProp,
  syntaxTree,
  syntaxTreeAvailable,
  unfoldEffect,
} from "@codemirror/language";
import {
  Annotation,
  type EditorState,
  StateEffect,
  StateField,
} from "@codemirror/state";
import { type EditorView } from "@codemirror/view";
import { type SyntaxNode } from "@lezer/common";

export const SYNTAX_TREE_PARSING_TIMEOUT = 1000 * 60;

export const ensureParsedSyntaxTree = (
  view: EditorView,
  skipDispatch = false,
) => {
  // To process entire document instead of only visible part
  // taken from https://discuss.codemirror.net/t/folding-functions/4189/2
  // https://github.com/codemirror/dev/issues/762
  ensureSyntaxTree(
    view.state,
    view.state.doc.length,
    SYNTAX_TREE_PARSING_TIMEOUT,
  );
  if (!skipDispatch) {
    view.dispatch({});
  }
  const isSyntaxTreeAvailable = syntaxTreeAvailable(
    view.state,
    view.state.doc.length,
  );
  if (!isSyntaxTreeAvailable) {
    console.warn(
      `syntax tree not available after ${SYNTAX_TREE_PARSING_TIMEOUT}ms`,
    );
  }
};

export function getAllFoldPaths(view: EditorView) {
  ensureParsedSyntaxTree(view);
  const tree = syntaxTree(view.state);
  const paths: {
    path: string;
    range: {
      from: number;
      to: number;
    };
  }[] = [];
  tree.iterate({
    enter({ node }) {
      const foldProp = node.type.prop(foldNodeProp);
      if (!foldProp) {
        return;
      }

      const path = getFoldPathKey(view.state, node);
      const range = foldProp(node, view.state);
      if (!range || range.from >= range.to) {
        return;
      }
      paths.push({ path, range });
    },
  });
  return paths;
}

const initializeFoldingAnnotation = Annotation.define<boolean>();
export function foldPaths(
  view: EditorView,
  paths: Record<string, boolean>,
  isCollapsed?: boolean,
) {
  const foldCount = Object.values(paths).filter((v) => v).length;
  if (!isCollapsed && foldCount === 0) {
    return;
  }

  const foldEffects: (StateEffect<unknown> | null)[] = getAllFoldPaths(
    view,
  ).flatMap(({ path, range }) => {
    if (isCollapsed || paths[path]) {
      return [foldEffect.of(range)];
    }
    return [];
  });

  if (!foldEffects.length) {
    return;
  }
  view.dispatch({
    annotations: initializeFoldingAnnotation.of(true),
    effects: [
      StateEffect.appendConfig.of(codeFolding()),
      ...foldEffects.filter((v) => v != null),
    ],
  });
}

export const foldTracker = ({
  setFoldState,
}: {
  setFoldState: SetValue<Record<string, boolean>>;
}) =>
  StateField.define({
    create: () => null,
    update: (value, tr) => {
      if (tr.annotation(initializeFoldingAnnotation)) {
        return value;
      }

      for (const e of tr.effects) {
        if (e.is(foldEffect)) {
          const path = getFoldPathKey(tr.state, e.value.from);
          setFoldState((prev) => ({
            ...prev,
            [path]: true,
          }));
        }
        if (e.is(unfoldEffect)) {
          const path = getFoldPathKey(tr.state, e.value.from);
          setFoldState((prev) => ({
            ...prev,
            [path]: false,
          }));
        }
      }
      return value;
    },
  });

function getFoldPathKey(state: EditorState, from: number | SyntaxNode) {
  const tree = syntaxTree(state);
  const cursor = (
    typeof from === "number" ? tree.resolve(from) : from
  ).cursor();

  const path = [];
  do {
    const jsonNode = parseJsonNode(cursor.node, state);
    if (jsonNode) {
      path.unshift(jsonNode);
      continue;
    }
    const yamlNode = parseYamlNode(cursor.node, state);
    if (yamlNode) {
      path.unshift(yamlNode);
    }
  } while (cursor.parent());

  return JSON.stringify(path);
}

// https://github.com/lezer-parser/json/blob/main/src/json.grammar
function parseJsonNode(node: SyntaxNode, state: EditorState) {
  if (node.type.name === "Property") {
    const propertyNameNode = node.firstChild;
    if (!propertyNameNode) {
      return null;
    }
    // remove quotes so we can share keys between json and yaml
    const propertyName = state.sliceDoc(
      propertyNameNode.from + 1,
      propertyNameNode.to - 1,
    );
    if (propertyName) {
      return propertyName;
    }
  } else if (node.parent?.type.name === "Array") {
    const cursor = node.parent.cursor();
    cursor.firstChild();
    let i = 0;
    while (cursor.nextSibling()) {
      if (isSameNode(cursor.node, node)) {
        // keep index as string to keep parity with treeView paths
        return [`${i}`];
      }
      i++;
    }
  }
}

function isSameNode(a: SyntaxNode, b: SyntaxNode): boolean {
  return a.type === b.type && a.from === b.from && a.to === b.to;
}

// https://github.com/lezer-parser/yaml/blob/main/src/yaml.grammar
function parseYamlNode(node: SyntaxNode, state: EditorState) {
  if (node.type.name === "Pair") {
    const keyNode = node.firstChild;
    if (keyNode) {
      return state.sliceDoc(keyNode.from, keyNode.to);
    }
  } else if (node.type.name === "Item") {
    const cursor = node.parent?.cursor();
    if (!cursor) {
      return;
    }

    cursor.firstChild();
    let i = 0;
    while (cursor.nextSibling()) {
      if (isSameNode(cursor.node, node)) {
        // keep index as string to keep parity with treeView paths
        return [`${i}`];
      }
      if (cursor.name === "Item") {
        i++;
      }
    }
  } else if (node.type.name === "BlockSequence") {
    return [];
  }
  return;
}
