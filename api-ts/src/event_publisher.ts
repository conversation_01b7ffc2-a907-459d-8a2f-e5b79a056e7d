import {
  InvokeCommand,
  LambdaClient,
  ResourceNotFoundException,
  ServiceException,
} from "@aws-sdk/client-lambda";
import { deterministicReplacer, mapAt } from "@braintrust/core";
import {
  EncryptedMessage,
  encryptMessage,
  isCryptoAvailable,
} from "@braintrust/proxy/utils";
import {
  brainstoreEnabled,
  makeBrainstoreObjectIdFromParts,
} from "./brainstore/brainstore";
import {
  BroadcastSecret,
  SecretBroadcastKeyRequest,
  secretBroadcastKey,
} from "./broadcast_key";
import {
  ETL_PERIOD_S,
  runBrainstoreEtlLoopRequest,
  tryAcquireAdvisoryLock,
} from "./brainstore/backfill";
import { customFetchRequest } from "./custom_fetch";
import { getPG } from "./db/pg";
import {
  BRAINSTORE_DISABLE_ETL_LOOP,
  CATCHUP_ETL_ARN,
  IS_LOCAL,
  REALTIME_URL,
} from "./env";
import { PENDING_FLUSHABLES } from "./pending_flushables";
import { ObjectType } from "./schema";
import { extractErrorText, HTTPError, postDefaultHeaders } from "./util";
import { getLogger } from "./instrumentation/logger";
import pino from "pino";

let _lambdaClient: LambdaClient | null = null;
function getLambdaClient(): LambdaClient {
  if (!_lambdaClient) {
    _lambdaClient = new LambdaClient();
  }
  return _lambdaClient;
}

export type EventPublisherItem = {
  object_type: ObjectType;
  object_id: string;
  is_audit_log: boolean;
  type: string;
  value: unknown;
};

export type LogToPublisherFn = (item: EventPublisherItem) => void;

const EVENT_PUBLISHER_FLUSH_BATCH_SIZE = 100;
const EVENT_PUBLISHER_FLUSH_MAX_REQUEST_SIZE = 4 * 1024 * 1024;

type EventPublisherPublishItem = {
  channel: BroadcastSecret["channel"];
  type: EventPublisherItem["type"];
  value: EncryptedMessage;
};

// This class is used to broadcast written log data to realtime and trigger the
// ETL process.
export class EventPublisher {
  private appOrigin: string;
  private token: string | undefined;
  private events: EventPublisherItem[];
  private pino: pino.Logger;

  constructor(args: { appOrigin: string; token: string | undefined }) {
    this.appOrigin = args.appOrigin;
    this.token = args.token;
    this.events = [];
    this.pino = getLogger().child({
      task: "event-publisher",
    });
  }

  public log(event: EventPublisherItem) {
    this.events.push(event);
  }

  public async flush() {
    if (!this.events.length) {
      return;
    }
    if (isCryptoAvailable()) {
      while (await this.flushOnce()) {}
    }
  }

  private async flushOnce(): Promise<boolean> {
    const items: EventPublisherItem[] = [];
    const itemValuesSerialized: string[] = [];
    let itemsLen = 0;
    for (
      let i = 0;
      i < this.events.length &&
      i < EVENT_PUBLISHER_FLUSH_BATCH_SIZE &&
      itemsLen < EVENT_PUBLISHER_FLUSH_MAX_REQUEST_SIZE / 2;
      ++i
    ) {
      items.push(this.events[i]);
      // In order to avoid redundant serialization down below, we store the
      // serialized form of item.value separately.
      const { value, ...itemRest } = items[items.length - 1];
      const itemValueS = JSON.stringify(value);
      itemValuesSerialized.push(itemValueS);
      itemsLen += itemValueS.length + JSON.stringify(itemRest).length;
    }

    if (items.length === 0) {
      return false;
    }

    // Splice out the items we popped from `this.events`.
    this.events.splice(0, items.length);

    // Publish events to realtime.
    const channels = new Map<string, BroadcastSecret>();
    const itemsPromies: Promise<EventPublisherPublishItem>[] = [];
    const uniqueObjects = new Set();
    for (let i = 0; i < items.length; ++i) {
      const item = items[i];
      const itemValueS = itemValuesSerialized[i];
      const channelKey: SecretBroadcastKeyRequest = {
        object_type: item.object_type,
        id: item.object_id,
        audit_log: item.is_audit_log,
      };
      uniqueObjects.add(
        makeBrainstoreObjectIdFromParts(item.object_type, item.object_id),
      );
      const channelKeyS = JSON.stringify(channelKey, deterministicReplacer);
      if (!channels.has(channelKeyS)) {
        // Since this function is only called after we have logged events into
        // the DB, we assume the ACL check has been done.
        channels.set(
          channelKeyS,
          await secretBroadcastKey({
            ...channelKey,
            appOrigin: this.appOrigin,
            token: this.token,
            skipAclCheck: true,
          }),
        );
      }
      const key = mapAt(channels, channelKeyS);
      itemsPromies.push(
        (async () => {
          const encryptedMessage = await encryptMessage(key.token, itemValueS);
          if (!encryptedMessage) {
            // We should only flush if crypto is available.
            throw new Error("Impossible");
          }
          return {
            channel: key.channel,
            type: item.type,
            value: encryptedMessage,
          };
        })(),
      );
    }
    const publishItems = await Promise.all(itemsPromies);

    const startTime = new Date().getTime() / 1000;
    const publishItemsS = JSON.stringify(publishItems);
    const realtimeBroadcastPromise = async () => {
      try {
        const resp = await customFetchRequest(`${REALTIME_URL}/broadcast`, {
          method: "POST",
          headers: postDefaultHeaders({ token: this.token }),
          body: publishItemsS,
          timeout: 10000,
        });
        if (!resp.ok) {
          throw new HTTPError(resp.statusCode, await resp.body.text());
        }
      } catch (e) {
        const endTime = new Date().getTime() / 1000;
        const errText = extractErrorText(e);
        this.pino.error(
          {
            elapsed: endTime - startTime,
            payloadSize: publishItemsS.length,
            error: e,
          },
          `Broadcast request failed: ${errText}`,
        );
      }
    };

    PENDING_FLUSHABLES.add(realtimeBroadcastPromise());

    return true;
  }
}

export async function signalEtl() {
  const pino = getLogger().child({
    task: "signal-etl",
  });
  if (CATCHUP_ETL_ARN) {
    // This is just a heuristic -- if someone else is running ETL right now,
    // there's no point spinning up the lambda function (which costs ~29ms to
    // spin up, check, and then shut down).
    //
    // Note: this function does not hold onto the lock if it successfully
    // acquires it, because the query is executed in its own pool connection,
    // outside a transaction.
    {
      const conn = getPG();
      if (!(await tryAcquireAdvisoryLock(conn, "brainstore_etl"))) {
        return;
      }
    }
    const command = new InvokeCommand({
      FunctionName: CATCHUP_ETL_ARN,
      InvocationType: "Event",
      Payload: JSON.stringify({
        version: "0",
        id: "event-id",
        "detail-type": "event type",
        source: "event source",
        account: "AWS account ID",
        time: "event time",
        region: "AWS region",
        resources: [],
        detail: {},
      }),
    });
    try {
      await getLambdaClient().send(command);
    } catch (e) {
      if (e instanceof ResourceNotFoundException) {
        pino.error(
          {
            error: e,
          },
          `The Lambda function ${CATCHUP_ETL_ARN} was not found`,
        );
      } else if (e instanceof ServiceException) {
        pino.error(
          {
            error: e,
          },
          "The service returned an error",
        );
      } else {
        throw e;
      }
    }
  } else if (IS_LOCAL) {
    if (brainstoreEnabled() && !BRAINSTORE_DISABLE_ETL_LOOP) {
      // Not necessary to await the ETL loop invocation. Locally, the api-ts
      // server should continue running after the main request has completed,
      // allowing this to complete as well.
      runBrainstoreEtlLoopRequest({
        timeout: ETL_PERIOD_S,
      }).catch((e) => {
        pino.error(
          {
            error: e,
          },
          "Failed to run brainstore ETL loop",
        );
      });
    }
  }
}
