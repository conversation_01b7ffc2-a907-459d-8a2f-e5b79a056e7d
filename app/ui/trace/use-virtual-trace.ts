import { type DataObjectType } from "#/utils/btapi/btapi";
import { useFetchBtqlOptions } from "#/utils/btql/btql";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useBtqlQueryBuilder } from "#/utils/btql/use-query-builder";
import { useMemo } from "react";
import { type RealtimeState } from "@braintrust/local/app-schema";
import { type ModelCosts } from "#/ui/prompts/models";
import {
  makeTraceQueryKey,
  type TraceQueryKeyParams,
  makeTraceQueryFn,
  buildVirtualTrace,
} from "./loading/query-utils";
import { type PreviewTrace } from "#/ui/trace/graph";

export function useVirtualTrace({
  rowId,
  objectId,
  objectType,
  allowEmpty,
  modelCosts,
}: {
  rowId: string | null;
  objectId: string | null;
  objectType: DataObjectType;
  allowEmpty?: boolean;
  modelCosts?: Record<string, ModelCosts>;
  grouping?: string;
}): {
  trace: PreviewTrace | null;
  buildTraceError: Error | null;
  hasNoData: boolean;
  comparisonKey: string;
  hasLoaded: boolean | undefined;
  isPending: boolean;
  isQueryLoading: boolean;
  realtimeState: RealtimeState | undefined;
} {
  const builder = useBtqlQueryBuilder({});
  const btqlOptions = useFetchBtqlOptions();

  const queryClient = useQueryClient();

  const isQueryEnabled = !!rowId && !!objectId;
  const queryKeyParams: TraceQueryKeyParams = {
    objectType,
    objectId,
    traceRowId: rowId,
  };
  // Fetch the full list of spans in a trace up front. Eventually, we might want to virtualize
  // this too, but for now, we assume that it's fast enough.
  const {
    data: spanTreeData,
    isLoading,
    isPending,
    isPlaceholderData,
  } = useQuery(
    {
      queryKey: makeTraceQueryKey(queryKeyParams),
      enabled: isQueryEnabled,
      queryFn: makeTraceQueryFn(queryKeyParams, builder, btqlOptions),
      throwOnError: false,
      staleTime: Infinity,
    },
    queryClient,
  );

  const hasQueryResult = !!spanTreeData && !!spanTreeData.queryData.data;
  const { trace, error: buildTraceError } = useMemo(() => {
    if ((!isQueryEnabled && allowEmpty) || !hasQueryResult) {
      return { trace: null, error: null };
    }
    return buildVirtualTrace(
      spanTreeData.queryData.data ?? [],
      spanTreeData.id ?? null,
      modelCosts,
    );
  }, [
    spanTreeData?.queryData.data,
    spanTreeData?.id,
    isQueryEnabled,
    allowEmpty,
    modelCosts,
    hasQueryResult,
  ]);

  const isTraceQueryLoading = isPlaceholderData || isLoading;

  return {
    trace,
    buildTraceError: buildTraceError,
    hasNoData: !!spanTreeData && spanTreeData.queryData.data.length === 0,
    isQueryLoading: isTraceQueryLoading,
    comparisonKey: "", // XXX TODO
    hasLoaded: !isLoading,
    isPending,
    realtimeState: spanTreeData?.queryData.realtime_state,
  };
}
