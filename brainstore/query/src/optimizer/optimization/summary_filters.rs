use std::collections::HashSet;

use btql::binder::ast::{
    flatten_boolean_expr, BooleanOp, ComparisonOp, Field, TransformExpr, UnaryOp,
};
use btql::binder::types::scalar_type_to_logical_schema;
use btql::binder::Expr;
use storage::process_wal::{PAGINATION_KEY_FIELD, XACT_ID_FIELD};
use util::json::PathPiece;

use crate::interpreter::tantivy::expand::CREATED_FIELD;
use crate::optimizer::ast::CursorField;
use crate::optimizer::error::Result;
use crate::optimizer::optimizer::OptimizerContext;

pub fn split_post_aggregation_filters(
    ctx: &OptimizerContext,
    filter: Option<Expr>,
    cursor_field: Option<CursorField>,
    weighted_scores: &HashSet<String>,
) -> Result<(Option<Expr>, Option<Expr>)> {
    let filter = if let Some(filter) = filter {
        filter
    } else {
        return Ok((None, None));
    };

    match filter {
        Expr::Boolean {
            op: BooleanOp::And,
            children,
        } => {
            let mut base_filters = Vec::new();
            let mut post_aggregation_fields = Vec::new();

            for child in children {
                let (filter, post_aggregation_field) = split_post_aggregation_filters(
                    ctx,
                    Some(*child.clone()),
                    cursor_field,
                    weighted_scores,
                )?;

                if let Some(filter) = filter {
                    base_filters.push(filter);
                }
                if let Some(post_aggregation_field) = post_aggregation_field {
                    post_aggregation_fields.push(post_aggregation_field);
                }
            }

            Ok((
                flatten_boolean_expr(BooleanOp::And, base_filters),
                flatten_boolean_expr(BooleanOp::And, post_aggregation_fields),
            ))
        }
        Expr::Boolean {
            op: BooleanOp::Or,
            children,
        } => {
            let mut base_filters = Vec::new();
            let mut post_aggregation_fields = Vec::new();

            let mut all_pushed_down = true;
            for child in children.clone() {
                let (base_filter, post_aggregation_field) = split_post_aggregation_filters(
                    ctx,
                    Some(*child.clone()),
                    cursor_field,
                    weighted_scores,
                )?;

                if let Some(base_filter) = base_filter {
                    base_filters.push(base_filter);
                } else {
                    all_pushed_down = false;
                }
                if let Some(post_aggregation_field) = post_aggregation_field {
                    post_aggregation_fields.push(post_aggregation_field);
                }
            }

            // If we can't push down any branch of an OR, then we can't push down any part of the OR.
            if all_pushed_down {
                return Ok((
                    flatten_boolean_expr(BooleanOp::Or, base_filters),
                    flatten_boolean_expr(BooleanOp::Or, post_aggregation_fields),
                ));
            } else {
                return Ok((
                    None,
                    Some(Expr::Boolean {
                        op: BooleanOp::Or,
                        children: children,
                    }),
                ));
            }
        }
        Expr::Comparison { op, left, right } => {
            if is_paginated_field(&left) || is_paginated_field(&right) {
                let expr = Expr::Comparison { op, left, right };
                // Repeat the filter post-aggregation
                Ok((Some(expr.clone()), Some(expr)))
            } else {
                split_if_has_post_aggregation_fields(
                    Expr::Comparison { op, left, right },
                    weighted_scores,
                )
            }
        }
        e => split_if_has_post_aggregation_fields(e, weighted_scores),
    }
}

fn is_paginated_field(expr: &Expr) -> bool {
    match expr {
        Expr::Field(Field { name, .. }) => {
            if name.len() != 1 {
                return false;
            }
            match &name[0] {
                PathPiece::Key(s) => {
                    s == PAGINATION_KEY_FIELD || s == XACT_ID_FIELD || s == CREATED_FIELD
                }
                _ => return false,
            }
        }
        _ => false,
    }
}

fn split_if_has_post_aggregation_fields(
    expr: Expr,
    weighted_scores: &HashSet<String>,
) -> Result<(Option<Expr>, Option<Expr>)> {
    if can_pushdown_scores_filter(&expr, weighted_scores) {
        Ok((Some(expr.clone()), Some(expr)))
    } else if has_any_root_field(&expr, &POST_AGGREGATION_FIELDS) {
        Ok((None, Some(expr)))
    } else {
        Ok((Some(expr), None))
    }
}

const POST_AGGREGATION_FIELDS: [&str; 2] = ["metrics", "scores"];

fn has_any_root_field(expr: &Expr, fields: &[&str]) -> bool {
    let mut has_post_aggregation_field = false;
    let mut is_post_aggregation_field = |e: &Expr| {
        if has_post_aggregation_field {
            return;
        }
        if let Expr::Field(field) = e {
            if field.name.len() == 0 {
                return;
            }
            match &field.name[0] {
                PathPiece::Key(key) => {
                    has_post_aggregation_field =
                        has_post_aggregation_field || fields.contains(&key.as_str());
                }
                PathPiece::Index(_) => {}
            }
        }
    };

    expr.traverse(&mut is_post_aggregation_field);
    has_post_aggregation_field
}

fn can_pushdown_scores_filter(expr: &Expr, weighted_scores: &HashSet<String>) -> bool {
    match expr {
        Expr::Comparison {
            op: ComparisonOp::Eq | ComparisonOp::Ne,
            left,
            right,
        } => match (left.as_ref(), right.as_ref()) {
            (Expr::Field(Field { name, .. }), Expr::Literal(lit))
                if name.len() >= 2
                    && matches!(&name[0], PathPiece::Key(s) if s == "scores")
                    && !matches!(&name[1], PathPiece::Key(s) if weighted_scores.contains(s))
                    && matches!(&lit.value, serde_json::Value::Number(n)
                        if (n.is_i64() && matches!(n.as_i64(), Some(0) | Some(1)))
                        || (n.is_f64() && matches!(n.as_f64(), Some(0.0) | Some(1.0)))) =>
            {
                return true;
            }
            (Expr::Literal(lit), Expr::Field(Field { name, .. }))
                if name.len() >= 2
                    && matches!(&name[0], PathPiece::Key(s) if s == "scores")
                    && !matches!(&name[1], PathPiece::Key(s) if weighted_scores.contains(s))
                    && matches!(&lit.value, serde_json::Value::Number(n)
                        if (n.is_i64() && matches!(n.as_i64(), Some(0) | Some(1)))
                        || (n.is_f64() && matches!(n.as_f64(), Some(0.0) | Some(1.0)))) =>
            {
                return true;
            }
            _ => {}
        },
        Expr::Comparison {
            op: ComparisonOp::Gt | ComparisonOp::Ge | ComparisonOp::Lt | ComparisonOp::Le,
            left,
            right,
        } => match (left.as_ref(), right.as_ref()) {
            (Expr::Field(Field { name, .. }), Expr::Literal(_))
                if name.len() >= 2
                    && matches!(&name[0], PathPiece::Key(s) if s == "scores")
                    && !matches!(&name[1], PathPiece::Key(s) if weighted_scores.contains(s)) =>
            {
                return true;
            }
            (Expr::Literal(_), Expr::Field(Field { name, .. }))
                if name.len() >= 2
                    && matches!(&name[0], PathPiece::Key(s) if s == "scores")
                    && !matches!(&name[1], PathPiece::Key(s) if weighted_scores.contains(s)) =>
            {
                return true;
            }
            _ => {}
        },
        Expr::Unary {
            op: UnaryOp::IsNull | UnaryOp::IsNotNull,
            expr,
        } => match expr.as_ref() {
            Expr::Field(Field { name, .. })
                if name.len() >= 2
                    && matches!(&name[0], PathPiece::Key(s) if s == "scores")
                    && !matches!(&name[1], PathPiece::Key(s) if weighted_scores.contains(s)) =>
            {
                return true;
            }
            _ => {}
        },
        _ => {}
    };
    false
}

pub fn mark_negative_is_root_filters(filter: Expr) -> Result<Expr> {
    let mut rewrite_filter = |expr: Expr| match expr {
        Expr::Unary {
            op: UnaryOp::Not,
            expr,
        } => {
            if let Expr::Includes { haystack, .. } = expr.as_ref() {
                if let Expr::Field(Field { name, .. }) = haystack.as_ref() {
                    if name.len() == 1 && matches!(&name[0], PathPiece::Key(s) if s == "tags") {
                        return Expr::Boolean {
                            op: BooleanOp::And,
                            children: vec![
                                Box::new(Expr::Field(Field::new(
                                    vec![PathPiece::Key("is_root".to_string())],
                                    scalar_type_to_logical_schema(
                                        btql::schema::ScalarType::Boolean,
                                    ),
                                    None,
                                ))),
                                Box::new(Expr::Unary {
                                    op: UnaryOp::Not,
                                    expr,
                                }),
                            ],
                        };
                    }
                }
            }
            Expr::Unary {
                op: UnaryOp::Not,
                expr,
            }
        }
        f => f,
    };

    Ok(filter.transform(&mut rewrite_filter))
}
